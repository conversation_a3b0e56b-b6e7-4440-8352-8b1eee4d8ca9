<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Overlay Bar - معرض مكونات OBS</title>
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
      color: #333;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      padding: 40px;
      text-align: center;
    }

    .header h1 {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 15px;
    }

    .header p {
      font-size: 1.3rem;
      opacity: 0.9;
    }

    .content {
      padding: 40px;
    }

    .components-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 30px;
      margin-top: 30px;
    }

    .component-card {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 30px;
      border: 1px solid #e9ecef;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .component-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .component-icon {
      font-size: 3rem;
      margin-bottom: 20px;
      display: block;
    }

    .component-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 15px;
      color: #2c3e50;
    }

    .component-description {
      color: #6c757d;
      margin-bottom: 25px;
      line-height: 1.6;
    }

    .component-links {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 8px;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      font-family: 'Tajawal', Arial, sans-serif;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background: #0056b3;
      transform: translateY(-2px);
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
      transform: translateY(-2px);
    }

    .btn-success {
      background: #28a745;
      color: white;
    }

    .btn-success:hover {
      background: #1e7e34;
      transform: translateY(-2px);
    }

    .status-badge {
      position: absolute;
      top: 15px;
      right: 15px;
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .status-available {
      background: #d4edda;
      color: #155724;
    }

    .status-coming-soon {
      background: #fff3cd;
      color: #856404;
    }

    .preview-section {
      margin-top: 20px;
      padding: 15px;
      background: white;
      border-radius: 10px;
      border: 1px solid #dee2e6;
    }

    .preview-title {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 10px;
      color: #495057;
    }

    .preview-frame {
      width: 100%;
      height: 150px;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #6c757d;
      font-size: 0.9rem;
    }

    .instructions {
      background: #e8f4fd;
      border: 1px solid #bee5eb;
      border-radius: 15px;
      padding: 30px;
      margin-bottom: 30px;
    }

    .instructions h3 {
      color: #0c5460;
      margin-bottom: 15px;
      font-size: 1.3rem;
    }

    .instructions ol {
      color: #0c5460;
      padding-right: 20px;
    }

    .instructions li {
      margin-bottom: 8px;
      line-height: 1.5;
    }

    @media (max-width: 768px) {
      .components-grid {
        grid-template-columns: 1fr;
      }
      
      .header h1 {
        font-size: 2rem;
      }
      
      .content {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🎮 Overlay Bar</h1>
      <p>معرض مكونات OBS و TikTok Live Studio</p>
    </div>

    <div class="content">
      <!-- تعليمات الاستخدام -->
      <div class="instructions">
        <h3>📋 كيفية الاستخدام</h3>
        <ol>
          <li>اختر المكون الذي تريد استخدامه من القائمة أدناه</li>
          <li>انسخ رابط "للـ OBS" وأضفه كـ Browser Source في OBS</li>
          <li>استخدم رابط "لوحة التحكم" للتحكم في المكون</li>
          <li>اضبط الإعدادات حسب احتياجاتك</li>
        </ol>
      </div>

      <!-- شبكة المكونات -->
      <div class="components-grid">
        
        <!-- مكون المكاسب والخسائر -->
        <div class="component-card">
          <span class="status-badge status-available">متاح</span>
          <span class="component-icon">🏆💔</span>
          <h3 class="component-title">عداد المكاسب والخسائر</h3>
          <p class="component-description">
            عداد بصري لتتبع المكاسب والخسائر في الألعاب مع أزرار سريعة للتحكم اليدوي
          </p>
          <div class="component-links">
            <a href="/over%20bar/win-loss-counter/display.html" class="btn btn-primary" target="_blank">للـ OBS</a>
            <a href="/over%20bar/win-loss-counter/control.html" class="btn btn-secondary" target="_blank">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              🏆 المكاسب: 0 | 💔 الخسائر: 0
            </div>
          </div>
        </div>

        <!-- مكون التايمر -->
        <div class="component-card">
          <span class="status-badge status-coming-soon">قريباً</span>
          <span class="component-icon">⏱️</span>
          <h3 class="component-title">التايمر</h3>
          <p class="component-description">
            مؤقت قابل للتخصيص لتتبع الوقت في البث أو الألعاب مع إنذارات وتنبيهات
          </p>
          <div class="component-links">
            <a href="#" class="btn btn-primary" style="opacity: 0.5; cursor: not-allowed;">للـ OBS</a>
            <a href="#" class="btn btn-secondary" style="opacity: 0.5; cursor: not-allowed;">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              ⏱️ 00:00:00
            </div>
          </div>
        </div>

        <!-- مكون السكور -->
        <div class="component-card">
          <span class="status-badge status-coming-soon">قريباً</span>
          <span class="component-icon">🎯</span>
          <h3 class="component-title">لوحة النقاط</h3>
          <p class="component-description">
            عرض النقاط والإحصائيات مع دعم فرق متعددة وتحديث مباشر
          </p>
          <div class="component-links">
            <a href="#" class="btn btn-primary" style="opacity: 0.5; cursor: not-allowed;">للـ OBS</a>
            <a href="#" class="btn btn-secondary" style="opacity: 0.5; cursor: not-allowed;">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              🎯 الفريق الأول: 0 | الفريق الثاني: 0
            </div>
          </div>
        </div>

        <!-- مكون الإشعارات -->
        <div class="component-card">
          <span class="status-badge status-coming-soon">قريباً</span>
          <span class="component-icon">🔔</span>
          <h3 class="component-title">الإشعارات المباشرة</h3>
          <p class="component-description">
            عرض إشعارات المتابعين الجدد والهدايا والتعليقات المهمة
          </p>
          <div class="component-links">
            <a href="#" class="btn btn-primary" style="opacity: 0.5; cursor: not-allowed;">للـ OBS</a>
            <a href="#" class="btn btn-secondary" style="opacity: 0.5; cursor: not-allowed;">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              🔔 متابع جديد: أحمد محمد
            </div>
          </div>
        </div>

        <!-- مكون الإحصائيات -->
        <div class="component-card">
          <span class="status-badge status-coming-soon">قريباً</span>
          <span class="component-icon">📊</span>
          <h3 class="component-title">الإحصائيات المباشرة</h3>
          <p class="component-description">
            عرض إحصائيات البث المباشر مثل عدد المشاهدين والمتابعين والهدايا
          </p>
          <div class="component-links">
            <a href="#" class="btn btn-primary" style="opacity: 0.5; cursor: not-allowed;">للـ OBS</a>
            <a href="#" class="btn btn-secondary" style="opacity: 0.5; cursor: not-allowed;">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              📊 المشاهدين: 0 | المتابعين: 0
            </div>
          </div>
        </div>

        <!-- مكون الطقس -->
        <div class="component-card">
          <span class="status-badge status-coming-soon">قريباً</span>
          <span class="component-icon">🌤️</span>
          <h3 class="component-title">حالة الطقس</h3>
          <p class="component-description">
            عرض حالة الطقس الحالية لموقعك أو أي مدينة تختارها
          </p>
          <div class="component-links">
            <a href="#" class="btn btn-primary" style="opacity: 0.5; cursor: not-allowed;">للـ OBS</a>
            <a href="#" class="btn btn-secondary" style="opacity: 0.5; cursor: not-allowed;">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              🌤️ الرياض: --°C
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>

  <script>
    // إضافة تأثيرات تفاعلية
    document.addEventListener('DOMContentLoaded', function() {
      const cards = document.querySelectorAll('.component-card');
      
      cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';
        });
      });
      
      // منع النقر على الروابط المعطلة
      const disabledLinks = document.querySelectorAll('a[style*="opacity: 0.5"]');
      disabledLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          alert('هذا المكون غير متاح حالياً. سيتم إضافته قريباً!');
        });
      });
      
      console.log('🎮 Overlay Bar Gallery loaded');
    });
  </script>
</body>
</html>
