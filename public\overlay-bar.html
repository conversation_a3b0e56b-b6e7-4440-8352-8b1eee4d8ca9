<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Overlay Bar - معرض مكونات OBS</title>
  <!-- تحميل سكريبت الوضع المظلم قبل أي شيء آخر لمنع الوميض -->
  <script src="/preload-theme.js"></script>
  <link rel="stylesheet" href="/dark-mode.css">
  <link rel="stylesheet" href="/css/loading-indicator.css">
  <link rel="stylesheet" href="/css/sidebar-enhanced.css">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      color-scheme: light dark;
    }

    body {
      font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-gradient);
      color: var(--text-color);
      transition: background 0.3s ease, color 0.3s ease;
      min-height: 100vh;
      display: flex;
    }

    .sidebar {
      width: 250px;
      background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
      box-shadow: 3px 0 15px rgba(0, 0, 0, 0.08);
      padding: 0;
      position: fixed;
      height: 100%;
      right: 0;
      top: 0;
      overflow-y: auto;
      z-index: 1000;
      border-left: 1px solid rgba(0, 0, 0, 0.05);
      border-top-left-radius: 15px;
      border-bottom-left-radius: 15px;
      opacity: 0.95 !important;
    }

    .logo {
      text-align: center;
      padding: 25px 15px;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
      position: relative;
      border-top-left-radius: 15px;
    }

    .logo h3 {
      color: white;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .nav-menu {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 20px 15px;
    }

    .nav-menu a {
      display: flex;
      align-items: center;
      padding: 14px 18px;
      color: #444;
      text-decoration: none;
      border-radius: 12px;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    }

    .nav-menu a:hover {
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      transform: translateX(-3px);
      box-shadow: 0 4px 12px rgba(255, 59, 92, 0.3);
    }

    .nav-menu a.active {
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      box-shadow: 0 4px 12px rgba(255, 59, 92, 0.3);
    }

    .nav-icon {
      margin-left: 12px;
      font-size: 1.2rem;
    }

    .main-content {
      margin-right: 250px;
      padding: 20px 40px;
      background: var(--card-bg);
      min-height: 100vh;
      transition: background 0.3s ease;
      width: calc(100% - 250px);
      box-sizing: border-box;
    }

    .frost-light-container {
      position: relative;
      display: block;
      margin: 0 auto;
      text-align: center;
      width: 100%;
      padding-top: 40px;
    }

    .frost-light-container h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 15px;
      color: var(--text-color);
      text-shadow: 0 0 10px rgba(135,206,250,0.5);
    }

    .frost-light-container p {
      font-size: 1.2rem;
      color: var(--text-secondary);
      margin-bottom: 40px;
    }

    .components-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 25px;
      margin-top: 30px;
      width: 100%;
      max-width: none;
    }

    .component-card {
      background: var(--card-bg);
      border-radius: 15px;
      padding: 30px;
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .component-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
      border-color: var(--primary-color);
    }

    .component-icon {
      font-size: 3rem;
      margin-bottom: 20px;
      display: block;
    }

    .component-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 15px;
      color: var(--text-color);
    }

    .component-description {
      color: var(--text-secondary);
      margin-bottom: 25px;
      line-height: 1.6;
    }

    .component-links {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      font-family: 'Tajawal', sans-serif;
      box-shadow: 0 4px 6px rgba(255, 59, 92, 0.2);
    }

    .btn-primary {
      background: var(--primary-gradient);
      color: var(--button-text);
    }

    .btn-primary:hover {
      background-color: #e6354f;
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(255, 59, 92, 0.25);
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
      transform: translateY(-2px);
    }

    .btn-success {
      background: #28a745;
      color: white;
    }

    .btn-success:hover {
      background: #1e7e34;
      transform: translateY(-2px);
    }

    .status-badge {
      position: absolute;
      top: 15px;
      right: 15px;
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .status-available {
      background: #d4edda;
      color: #155724;
    }

    .status-coming-soon {
      background: #fff3cd;
      color: #856404;
    }

    .preview-section {
      margin-top: 20px;
      padding: 15px;
      background: var(--input-bg);
      border-radius: 10px;
      border: 1px solid var(--border-color);
    }

    .preview-title {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 10px;
      color: var(--text-color);
    }

    .preview-frame {
      width: 100%;
      height: 150px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background: var(--table-header-bg);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    .instructions {
      background: var(--table-header-bg);
      border: 1px solid var(--border-color);
      border-radius: 15px;
      padding: 30px;
      margin-bottom: 30px;
      transition: background-color 0.3s ease;
    }

    .instructions h3 {
      color: var(--text-color);
      margin-bottom: 15px;
      font-size: 1.3rem;
    }

    .instructions ol {
      color: var(--text-color);
      padding-right: 20px;
    }

    .instructions li {
      margin-bottom: 8px;
      line-height: 1.5;
    }

    .footer {
      text-align: center;
      margin-top: 40px;
      color: var(--text-secondary);
      font-size: 14px;
    }

    @media (max-width: 768px) {
      .components-grid {
        grid-template-columns: 1fr;
      }
      
      .header h1 {
        font-size: 2rem;
      }
      
      .content {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <h3>StreamTok</h3>
    </div>
    <div class="nav-menu">
      <a href="/connection.html">
        <span class="nav-icon">🔌</span>
        الاتصال بـ TikTok Live
      </a>
      <a href="/mappings.html">
        <span class="nav-icon">🛠️</span>
        ربط الهدايا بالإجراءات
      </a>
      <a href="/tts-comments.html">
        <span class="nav-icon">🔊</span>
        قراءة التعليقات
      </a>
      <a href="/games.html">
        <span class="nav-icon">🎯</span>
        الألعاب
      </a>
      <a href="/settings.html">
        <span class="nav-icon">⚙️</span>
        الإعدادات
      </a>
      <a href="/profiles.html">
        <span class="nav-icon">👤</span>
        الملفات الشخصية
      </a>
      <a href="/subscriptions.html">
        <span class="nav-icon">⚡</span>
        الاشتراكات
      </a>
      <a href="/contact.html">
        <span class="nav-icon">📞</span>
        اتصل بنا
      </a>
      <a href="/overlay.html" target="_blank">
        <span class="nav-icon">🖥️</span>
        فتح Overlay
      </a>
      <a href="/overlay-bar.html" class="active">
        <span class="nav-icon">🎮</span>
        Overlay Bar
      </a>
    </div>
  </div>

  <div class="main-content">
    <div class="frost-light-container">
      <h1>🎮 Overlay Bar</h1>
      <p>معرض مكونات OBS و TikTok Live Studio</p>
    </div>
      <!-- تعليمات الاستخدام -->
      <div class="instructions">
        <h3>📋 كيفية الاستخدام</h3>
        <ol>
          <li>اختر المكون الذي تريد استخدامه من القائمة أدناه</li>
          <li>انسخ رابط "للـ OBS" وأضفه كـ Browser Source في OBS</li>
          <li>استخدم رابط "لوحة التحكم" للتحكم في المكون</li>
          <li>اضبط الإعدادات حسب احتياجاتك</li>
          <li><strong>🔥 جديد:</strong> اختصارات عالمية تعمل من أي مكان!</li>
        </ol>

        <div style="background: #e8f4fd; padding: 15px; border-radius: 8px; margin-top: 15px; border: 1px solid #bee5eb;">
          <h4 style="color: #0c5460; margin-bottom: 10px;">⌨️ الاختصارات العالمية</h4>
          <p style="color: #0c5460; margin: 5px 0;"><strong>Ctrl + Alt + ↑</strong> ← زيادة المكاسب</p>
          <p style="color: #0c5460; margin: 5px 0;"><strong>Ctrl + Alt + ↓</strong> ← زيادة الخسائر</p>
          <p style="color: #0c5460; margin: 10px 0 5px 0; font-size: 14px;">
            ✨ <strong>تعمل من أي مكان</strong> - حتى أثناء اللعب!
          </p>
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            💡 تحتاج تثبيت: <code>npm install node-global-key-listener</code>
          </p>
        </div>
      </div>

      <!-- شبكة المكونات -->
      <div class="components-grid">
        
        <!-- مكون المكاسب والخسائر -->
        <div class="component-card">
          <span class="status-badge status-available">متاح</span>
          <span class="component-icon">🏆💔</span>
          <h3 class="component-title">عداد المكاسب والخسائر</h3>
          <p class="component-description">
            عداد بصري لتتبع المكاسب والخسائر في الألعاب مع أزرار سريعة للتحكم اليدوي
            <br><strong>✨ جديد:</strong> أسماء مخصصة + اختصارات لوحة المفاتيح (Ctrl+Alt+↑/↓)
          </p>
          <div class="component-links">
            <a href="/over%20bar/win-loss-counter/display.html" class="btn btn-primary" target="_blank">للـ OBS</a>
            <a href="/over%20bar/win-loss-counter/control.html" class="btn btn-secondary" target="_blank">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              🏆 المكاسب: 0 | 💔 الخسائر: 0
              <br><small>⌨️ Ctrl+Alt+↑/↓ للتحكم السريع</small>
            </div>
          </div>
        </div>

        <!-- مكون التايمر -->
        <div class="component-card">
          <span class="status-badge status-coming-soon">قريباً</span>
          <span class="component-icon">⏱️</span>
          <h3 class="component-title">التايمر</h3>
          <p class="component-description">
            مؤقت قابل للتخصيص لتتبع الوقت في البث أو الألعاب مع إنذارات وتنبيهات
          </p>
          <div class="component-links">
            <a href="#" class="btn btn-primary" style="opacity: 0.5; cursor: not-allowed;">للـ OBS</a>
            <a href="#" class="btn btn-secondary" style="opacity: 0.5; cursor: not-allowed;">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              ⏱️ 00:00:00
            </div>
          </div>
        </div>

        <!-- مكون السكور -->
        <div class="component-card">
          <span class="status-badge status-coming-soon">قريباً</span>
          <span class="component-icon">🎯</span>
          <h3 class="component-title">لوحة النقاط</h3>
          <p class="component-description">
            عرض النقاط والإحصائيات مع دعم فرق متعددة وتحديث مباشر
          </p>
          <div class="component-links">
            <a href="#" class="btn btn-primary" style="opacity: 0.5; cursor: not-allowed;">للـ OBS</a>
            <a href="#" class="btn btn-secondary" style="opacity: 0.5; cursor: not-allowed;">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              🎯 الفريق الأول: 0 | الفريق الثاني: 0
            </div>
          </div>
        </div>

        <!-- مكون الإشعارات -->
        <div class="component-card">
          <span class="status-badge status-coming-soon">قريباً</span>
          <span class="component-icon">🔔</span>
          <h3 class="component-title">الإشعارات المباشرة</h3>
          <p class="component-description">
            عرض إشعارات المتابعين الجدد والهدايا والتعليقات المهمة
          </p>
          <div class="component-links">
            <a href="#" class="btn btn-primary" style="opacity: 0.5; cursor: not-allowed;">للـ OBS</a>
            <a href="#" class="btn btn-secondary" style="opacity: 0.5; cursor: not-allowed;">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              🔔 متابع جديد: أحمد محمد
            </div>
          </div>
        </div>

        <!-- مكون الإحصائيات -->
        <div class="component-card">
          <span class="status-badge status-coming-soon">قريباً</span>
          <span class="component-icon">📊</span>
          <h3 class="component-title">الإحصائيات المباشرة</h3>
          <p class="component-description">
            عرض إحصائيات البث المباشر مثل عدد المشاهدين والمتابعين والهدايا
          </p>
          <div class="component-links">
            <a href="#" class="btn btn-primary" style="opacity: 0.5; cursor: not-allowed;">للـ OBS</a>
            <a href="#" class="btn btn-secondary" style="opacity: 0.5; cursor: not-allowed;">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              📊 المشاهدين: 0 | المتابعين: 0
            </div>
          </div>
        </div>

        <!-- مكون الطقس -->
        <div class="component-card">
          <span class="status-badge status-coming-soon">قريباً</span>
          <span class="component-icon">🌤️</span>
          <h3 class="component-title">حالة الطقس</h3>
          <p class="component-description">
            عرض حالة الطقس الحالية لموقعك أو أي مدينة تختارها
          </p>
          <div class="component-links">
            <a href="#" class="btn btn-primary" style="opacity: 0.5; cursor: not-allowed;">للـ OBS</a>
            <a href="#" class="btn btn-secondary" style="opacity: 0.5; cursor: not-allowed;">لوحة التحكم</a>
          </div>
          <div class="preview-section">
            <div class="preview-title">معاينة:</div>
            <div class="preview-frame">
              🌤️ الرياض: --°C
            </div>
          </div>
        </div>

      </div>

      <div class="footer">
        StreamTok &copy; 2025
        - By : Abdelrahman Mohamed
      </div>
    </div>
  </div>

  <script src="/js/settings-manager.js"></script>
  <script src="/dark-mode.js"></script>
  <script>
    // إضافة تأثيرات تفاعلية
    document.addEventListener('DOMContentLoaded', function() {
      const cards = document.querySelectorAll('.component-card');
      
      cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';
        });
      });
      
      // منع النقر على الروابط المعطلة
      const disabledLinks = document.querySelectorAll('a[style*="opacity: 0.5"]');
      disabledLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          alert('هذا المكون غير متاح حالياً. سيتم إضافته قريباً!');
        });
      });
      
      console.log('🎮 Overlay Bar Gallery loaded');
    });
  </script>
</body>
</html>
