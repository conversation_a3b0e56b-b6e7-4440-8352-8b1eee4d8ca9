<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Overlay Bar - Win/Loss Counter</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: transparent;
      font-family: 'Arial', sans-serif;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .overlay-bar {
      background: rgba(0, 0, 0, 0.8);
      border-radius: 15px;
      padding: 15px 30px;
      display: flex;
      align-items: center;
      gap: 40px;
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      min-width: 400px;
      transition: all 0.3s ease;
    }

    .counter-section {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 24px;
      font-weight: bold;
      color: white;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .win-section {
      color: #00ff88;
    }

    .loss-section {
      color: #ff4757;
    }

    .counter-icon {
      font-size: 28px;
      filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
    }

    .counter-number {
      font-size: 32px;
      font-weight: 900;
      min-width: 60px;
      text-align: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 5px 10px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .separator {
      width: 2px;
      height: 40px;
      background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.3), transparent);
    }

    /* تأثيرات التحديث */
    .counter-number.updated {
      animation: pulse 0.3s ease;
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); background: rgba(255, 255, 255, 0.3); }
      100% { transform: scale(1); }
    }

    /* تأثيرات الهوفر للمعاينة */
    .overlay-bar:hover {
      transform: scale(1.02);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    }
  </style>
</head>
<body>
  <div class="overlay-bar">
    <div class="counter-section win-section">
      <span class="counter-icon">🏆</span>
      <span>المكاسب:</span>
      <span class="counter-number" id="winCounter">0</span>
    </div>
    
    <div class="separator"></div>
    
    <div class="counter-section loss-section">
      <span class="counter-icon">💔</span>
      <span>الخسائر:</span>
      <span class="counter-number" id="lossCounter">0</span>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script>
    // الاتصال بـ Socket.IO
    const socket = io('/overlay-bar');
    
    // عناصر العداد
    const winCounter = document.getElementById('winCounter');
    const lossCounter = document.getElementById('lossCounter');
    
    // تحديث العدادات
    function updateCounter(element, value) {
      element.textContent = value;
      element.classList.add('updated');
      setTimeout(() => {
        element.classList.remove('updated');
      }, 300);
    }
    
    // استقبال تحديثات العداد من الخادم
    socket.on('counterUpdate', (data) => {
      if (data.type === 'win') {
        updateCounter(winCounter, data.value);
      } else if (data.type === 'loss') {
        updateCounter(lossCounter, data.value);
      } else if (data.type === 'both') {
        updateCounter(winCounter, data.wins);
        updateCounter(lossCounter, data.losses);
      }
    });
    
    // طلب القيم الحالية عند التحميل
    socket.on('connect', () => {
      console.log('🔗 متصل بخادم Overlay Bar');
      socket.emit('getCounters');
    });
    
    // استقبال القيم الحالية
    socket.on('currentCounters', (data) => {
      winCounter.textContent = data.wins;
      lossCounter.textContent = data.losses;
    });
    
    // معالجة الأخطاء
    socket.on('disconnect', () => {
      console.log('❌ انقطع الاتصال بخادم Overlay Bar');
    });
    
    socket.on('connect_error', (error) => {
      console.error('خطأ في الاتصال:', error);
    });
  </script>
</body>
</html>
