/**
 * File Sync for Overlay Bar
 * مزامنة ملف البيانات مع localStorage
 */

(function() {
  'use strict';

  const STORAGE_KEY = 'overlayBar_winLossCounter';
  const DATA_FILE = '/over%20bar/data.json';
  
  // دالة لقراءة ملف JSON
  async function readJSONFile() {
    try {
      const response = await fetch(DATA_FILE + '?t=' + Date.now());
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.warn('فشل في قراءة الملف:', error);
    }
    return null;
  }
  
  // دالة لكتابة البيانات في localStorage
  function writeToStorage(data) {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('خطأ في كتابة localStorage:', error);
      return false;
    }
  }
  
  // دالة لقراءة البيانات من localStorage
  function readFromStorage() {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      return saved ? JSON.parse(saved) : null;
    } catch (error) {
      console.error('خطأ في قراءة localStorage:', error);
      return null;
    }
  }
  
  // دالة لمحاكاة كتابة الملف
  async function simulateFileWrite(data) {
    // في بيئة حقيقية، هذا سيحتاج endpoint في الخادم
    // الآن سنستخدم localStorage فقط
    
    const fileData = {
      winLossCounter: {
        wins: data.wins || 0,
        losses: data.losses || 0,
        lastUpdated: Date.now()
      }
    };
    
    // محاولة إرسال البيانات للخادم (ستفشل في المتصفح العادي)
    try {
      const response = await fetch('/api/update-overlay-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(fileData)
      });
      
      if (response.ok) {
        console.log('✅ تم تحديث الملف على الخادم');
        return true;
      }
    } catch (error) {
      // تجاهل الخطأ - هذا متوقع في المتصفح العادي
    }
    
    // استخدام localStorage كبديل
    return writeToStorage(data);
  }
  
  // دالة للمزامنة بين الملف و localStorage
  async function syncData() {
    try {
      const fileData = await readJSONFile();
      const localData = readFromStorage();
      
      if (fileData && fileData.winLossCounter) {
        const fileCounter = fileData.winLossCounter;
        
        // إذا لم توجد بيانات محلية، استخدم بيانات الملف
        if (!localData) {
          writeToStorage({
            wins: fileCounter.wins || 0,
            losses: fileCounter.losses || 0,
            lastUpdated: fileCounter.lastUpdated || Date.now()
          });
          return fileCounter;
        }
        
        // إذا كان الملف أحدث، استخدمه
        if ((fileCounter.lastUpdated || 0) > (localData.lastUpdated || 0)) {
          writeToStorage({
            wins: fileCounter.wins || 0,
            losses: fileCounter.losses || 0,
            lastUpdated: fileCounter.lastUpdated || Date.now()
          });
          return fileCounter;
        }
      }
      
      // استخدام البيانات المحلية
      return localData || { wins: 0, losses: 0, lastUpdated: Date.now() };
      
    } catch (error) {
      console.error('خطأ في المزامنة:', error);
      return readFromStorage() || { wins: 0, losses: 0, lastUpdated: Date.now() };
    }
  }
  
  // تصدير الدوال للاستخدام العام
  window.OverlayFileSync = {
    readFile: readJSONFile,
    writeStorage: writeToStorage,
    readStorage: readFromStorage,
    simulateWrite: simulateFileWrite,
    sync: syncData
  };
  
  // مزامنة تلقائية عند تحميل الصفحة
  document.addEventListener('DOMContentLoaded', function() {
    syncData().then(data => {
      console.log('🔄 تم تحميل البيانات:', data);
    });
  });
  
})();
