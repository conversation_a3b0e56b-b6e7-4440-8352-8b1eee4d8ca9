<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Win/Loss Counter - Control Panel</title>
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
      color: #333;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 10px;
    }

    .header p {
      font-size: 1.1rem;
      opacity: 0.9;
    }

    .content {
      padding: 40px;
    }

    .section {
      margin-bottom: 40px;
      padding: 30px;
      background: #f8f9fa;
      border-radius: 15px;
      border: 1px solid #e9ecef;
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 20px;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    /* الأزرار السريعة */
    .quick-buttons {
      display: flex;
      justify-content: center;
      gap: 30px;
      margin: 30px 0;
      padding: 25px;
      background: white;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .quick-btn {
      padding: 20px 40px;
      border: none;
      border-radius: 15px;
      font-size: 1.4rem;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      font-family: 'Tajawal', Arial, sans-serif;
      min-width: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .quick-btn-win {
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      color: white;
      box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
    }

    .quick-btn-win:hover {
      background: linear-gradient(135deg, #219a52, #27ae60);
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
    }

    .quick-btn-win:active {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(39, 174, 96, 0.5);
    }

    .quick-btn-loss {
      background: linear-gradient(135deg, #e74c3c, #ec7063);
      color: white;
      box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
    }

    .quick-btn-loss:hover {
      background: linear-gradient(135deg, #c0392b, #e74c3c);
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
    }

    .quick-btn-loss:active {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(231, 76, 60, 0.5);
    }

    .counter-display {
      display: flex;
      justify-content: center;
      gap: 60px;
      margin-bottom: 30px;
    }

    .counter-item {
      text-align: center;
      padding: 20px;
      background: white;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      min-width: 150px;
    }

    .counter-icon {
      font-size: 3rem;
      margin-bottom: 10px;
    }

    .counter-label {
      font-size: 1.2rem;
      font-weight: 500;
      margin-bottom: 10px;
    }

    .counter-value {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 15px;
    }

    .win-counter {
      color: #27ae60;
    }

    .loss-counter {
      color: #e74c3c;
    }

    .controls {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 20px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      font-family: 'Tajawal', Arial, sans-serif;
    }

    .btn-success {
      background: #27ae60;
      color: white;
    }

    .btn-success:hover {
      background: #219a52;
      transform: translateY(-2px);
    }

    .btn-danger {
      background: #e74c3c;
      color: white;
    }

    .btn-danger:hover {
      background: #c0392b;
      transform: translateY(-2px);
    }

    .btn-warning {
      background: #f39c12;
      color: white;
    }

    .btn-warning:hover {
      background: #d68910;
      transform: translateY(-2px);
    }

    .overlay-link {
      background: #17a2b8;
      color: white;
      padding: 15px;
      border-radius: 10px;
      text-align: center;
      margin-top: 20px;
    }

    .overlay-link a {
      color: white;
      text-decoration: none;
      font-weight: 500;
    }

    .overlay-link a:hover {
      text-decoration: underline;
    }

    @media (max-width: 768px) {
      .counter-display {
        flex-direction: column;
        gap: 20px;
      }
      
      .quick-buttons {
        flex-direction: column;
        gap: 15px;
      }
      
      .content {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🏆💔 Win/Loss Counter</h1>
      <p>لوحة التحكم في عدادات المكاسب والخسائر</p>
    </div>

    <div class="content">
      <!-- الأزرار السريعة -->
      <div class="section">
        <div class="section-title">
          ⚡ الأزرار السريعة
        </div>
        
        <div class="quick-buttons">
          <button class="quick-btn quick-btn-win" onclick="quickWin()">
            🏆 فوز
          </button>
          <button class="quick-btn quick-btn-loss" onclick="quickLoss()">
            💔 خسارة
          </button>
        </div>
      </div>

      <!-- عرض العدادات الحالية -->
      <div class="section">
        <div class="section-title">
          📊 العدادات الحالية
        </div>
        
        <div class="counter-display">
          <div class="counter-item">
            <div class="counter-icon">🏆</div>
            <div class="counter-label">المكاسب</div>
            <div class="counter-value win-counter" id="winDisplay">0</div>
            <div class="controls">
              <button class="btn btn-success" onclick="changeCounter('win', 1)">+</button>
              <button class="btn btn-danger" onclick="changeCounter('win', -1)">-</button>
            </div>
          </div>

          <div class="counter-item">
            <div class="counter-icon">💔</div>
            <div class="counter-label">الخسائر</div>
            <div class="counter-value loss-counter" id="lossDisplay">0</div>
            <div class="controls">
              <button class="btn btn-success" onclick="changeCounter('loss', 1)">+</button>
              <button class="btn btn-danger" onclick="changeCounter('loss', -1)">-</button>
            </div>
          </div>
        </div>

        <div style="text-align: center;">
          <button class="btn btn-warning" onclick="resetCounters()">🔄 إعادة تعيين الكل</button>
        </div>
      </div>

      <!-- رابط الـ Overlay -->
      <div class="overlay-link">
        <strong>رابط العرض للـ OBS:</strong><br>
        <a href="/over%20bar/win-loss-counter/display.html" target="_blank">
          http://localhost:3000/over%20bar/win-loss-counter/display.html
        </a>
      </div>
    </div>
  </div>

  <script>
    // مفتاح التخزين
    const STORAGE_KEY = 'overlayBar_winLossCounter';
    
    // عناصر الواجهة
    const winDisplay = document.getElementById('winDisplay');
    const lossDisplay = document.getElementById('lossDisplay');
    
    // تحميل البيانات من localStorage
    function loadCounters() {
      try {
        const saved = localStorage.getItem(STORAGE_KEY);
        if (saved) {
          const data = JSON.parse(saved);
          return {
            wins: data.wins || 0,
            losses: data.losses || 0
          };
        }
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
      }
      return { wins: 0, losses: 0 };
    }
    
    // حفظ البيانات في localStorage
    function saveCounters(wins, losses) {
      try {
        const data = {
          wins: wins,
          losses: losses,
          lastUpdated: Date.now()
        };
        localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
        return true;
      } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        return false;
      }
    }
    
    // تحديث العرض
    function updateDisplay() {
      const counters = loadCounters();
      winDisplay.textContent = counters.wins;
      lossDisplay.textContent = counters.losses;
    }
    
    // الأزرار السريعة
    function quickWin() {
      changeCounter('win', 1);
    }
    
    function quickLoss() {
      changeCounter('loss', 1);
    }
    
    // تغيير العداد
    function changeCounter(type, change) {
      const counters = loadCounters();
      
      if (type === 'win') {
        counters.wins = Math.max(0, counters.wins + change);
      } else if (type === 'loss') {
        counters.losses = Math.max(0, counters.losses + change);
      }
      
      saveCounters(counters.wins, counters.losses);
      updateDisplay();
      
      console.log(`${type === 'win' ? '🏆' : '💔'} ${type}: ${type === 'win' ? counters.wins : counters.losses}`);
    }
    
    // إعادة تعيين العدادات
    function resetCounters() {
      if (confirm('هل أنت متأكد من إعادة تعيين جميع العدادات؟')) {
        saveCounters(0, 0);
        updateDisplay();
        console.log('🔄 تم إعادة تعيين جميع العدادات');
      }
    }
    
    // تحديث العرض عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
      updateDisplay();
      console.log('🎮 Win/Loss Counter Control Panel loaded');
    });
    
    // تحديث دوري للتأكد من التزامن
    setInterval(updateDisplay, 2000);
  </script>
</body>
</html>
