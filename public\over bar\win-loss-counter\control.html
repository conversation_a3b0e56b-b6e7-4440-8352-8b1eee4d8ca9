<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Win/Loss Counter - Control Panel</title>
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
      color: #333;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 10px;
    }

    .header p {
      font-size: 1.1rem;
      opacity: 0.9;
    }

    .content {
      padding: 40px;
    }

    .section {
      margin-bottom: 40px;
      padding: 30px;
      background: #f8f9fa;
      border-radius: 15px;
      border: 1px solid #e9ecef;
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 20px;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    /* الأزرار السريعة */
    .quick-buttons {
      display: flex;
      justify-content: center;
      gap: 30px;
      margin: 30px 0;
      padding: 25px;
      background: white;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .quick-btn {
      padding: 20px 40px;
      border: none;
      border-radius: 15px;
      font-size: 1.4rem;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      font-family: 'Tajawal', Arial, sans-serif;
      min-width: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .quick-btn-win {
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      color: white;
      box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
    }

    .quick-btn-win:hover {
      background: linear-gradient(135deg, #219a52, #27ae60);
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
    }

    .quick-btn-win:active {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(39, 174, 96, 0.5);
    }

    .quick-btn-loss {
      background: linear-gradient(135deg, #e74c3c, #ec7063);
      color: white;
      box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
    }

    .quick-btn-loss:hover {
      background: linear-gradient(135deg, #c0392b, #e74c3c);
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
    }

    .quick-btn-loss:active {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(231, 76, 60, 0.5);
    }

    .counter-display {
      display: flex;
      justify-content: center;
      gap: 60px;
      margin-bottom: 30px;
    }

    .counter-item {
      text-align: center;
      padding: 20px;
      background: white;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      min-width: 150px;
    }

    .counter-icon {
      font-size: 3rem;
      margin-bottom: 10px;
    }

    .counter-label {
      font-size: 1.2rem;
      font-weight: 500;
      margin-bottom: 10px;
    }

    .counter-value {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 15px;
    }

    .win-counter {
      color: #27ae60;
    }

    .loss-counter {
      color: #e74c3c;
    }

    .controls {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 20px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      font-family: 'Tajawal', Arial, sans-serif;
    }

    .btn-success {
      background: #27ae60;
      color: white;
    }

    .btn-success:hover {
      background: #219a52;
      transform: translateY(-2px);
    }

    .btn-danger {
      background: #e74c3c;
      color: white;
    }

    .btn-danger:hover {
      background: #c0392b;
      transform: translateY(-2px);
    }

    .btn-warning {
      background: #f39c12;
      color: white;
    }

    .btn-warning:hover {
      background: #d68910;
      transform: translateY(-2px);
    }

    .overlay-link {
      background: #17a2b8;
      color: white;
      padding: 15px;
      border-radius: 10px;
      text-align: center;
      margin-top: 20px;
    }

    .overlay-link a {
      color: white;
      text-decoration: none;
      font-weight: 500;
    }

    .overlay-link a:hover {
      text-decoration: underline;
    }

    /* أنماط الإعدادات */
    .input-group {
      margin-bottom: 15px;
    }

    .input-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: var(--text-color);
    }

    .input-group input {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 5px;
      font-size: 14px;
      background: var(--input-bg);
      color: var(--text-color);
      box-sizing: border-box;
    }

    .input-group input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(255, 59, 92, 0.1);
    }

    /* أنماط الاختصارات */
    .shortcuts-info {
      background: var(--table-header-bg);
      padding: 15px;
      border-radius: 8px;
      border: 1px solid var(--border-color);
    }

    .shortcut-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid var(--border-color);
    }

    .shortcut-item:last-child {
      border-bottom: none;
    }

    .shortcut-key {
      background: var(--primary-color);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      font-weight: bold;
    }

    .shortcut-desc {
      color: var(--text-secondary);
      font-size: 14px;
    }

    @media (max-width: 768px) {
      .counter-display {
        flex-direction: column;
        gap: 20px;
      }
      
      .quick-buttons {
        flex-direction: column;
        gap: 15px;
      }
      
      .content {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🏆💔 Win/Loss Counter</h1>
      <p>لوحة التحكم في عدادات المكاسب والخسائر</p>
    </div>

    <div class="content">
      <!-- الأزرار السريعة -->
      <div class="section">
        <div class="section-title">
          ⚡ الأزرار السريعة
        </div>
        
        <div class="quick-buttons">
          <button class="quick-btn quick-btn-win" onclick="quickWin()">
            🏆 فوز
          </button>
          <button class="quick-btn quick-btn-loss" onclick="quickLoss()">
            💔 خسارة
          </button>
        </div>
      </div>

      <!-- إعدادات الأسماء -->
      <div class="section">
        <div class="section-title">
          ⚙️ إعدادات الأسماء
        </div>

        <div class="input-group">
          <label for="winLabelInput">اسم المكاسب:</label>
          <input type="text" id="winLabelInput" value="المكاسب" placeholder="مثال: الفوز، النقاط، المكاسب">
        </div>
        <div class="input-group">
          <label for="lossLabelInput">اسم الخسائر:</label>
          <input type="text" id="lossLabelInput" value="الخسائر" placeholder="مثال: الخسارة، الأخطاء، الهزائم">
        </div>
        <button class="btn btn-primary" onclick="updateLabels()">💾 حفظ الأسماء</button>
      </div>

      <!-- الاختصارات -->
      <div class="section">
        <div class="section-title">
          ⌨️ الاختصارات
        </div>
        <div class="shortcuts-info">
          <div class="shortcut-item">
            <span class="shortcut-key">Ctrl + Alt + ↑</span>
            <span class="shortcut-desc">زيادة المكاسب</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-key">Ctrl + Alt + ↓</span>
            <span class="shortcut-desc">زيادة الخسائر</span>
          </div>
        </div>
      </div>

      <!-- عرض العدادات الحالية -->
      <div class="section">
        <div class="section-title">
          📊 العدادات الحالية
        </div>
        
        <div class="counter-display">
          <div class="counter-item">
            <div class="counter-icon">🏆</div>
            <div class="counter-label" id="winLabelDisplay">المكاسب</div>
            <div class="counter-value win-counter" id="winDisplay">0</div>
            <div class="controls">
              <button class="btn btn-success" onclick="changeCounter('win', 1)">+</button>
              <button class="btn btn-danger" onclick="changeCounter('win', -1)">-</button>
            </div>
          </div>

          <div class="counter-item">
            <div class="counter-icon">💔</div>
            <div class="counter-label" id="lossLabelDisplay">الخسائر</div>
            <div class="counter-value loss-counter" id="lossDisplay">0</div>
            <div class="controls">
              <button class="btn btn-success" onclick="changeCounter('loss', 1)">+</button>
              <button class="btn btn-danger" onclick="changeCounter('loss', -1)">-</button>
            </div>
          </div>
        </div>

        <div style="text-align: center;">
          <button class="btn btn-warning" onclick="resetCounters()">🔄 إعادة تعيين الكل</button>
        </div>
      </div>

      <!-- رابط الـ Overlay -->
      <div class="overlay-link">
        <strong>رابط العرض للـ OBS:</strong><br>
        <a href="/over%20bar/win-loss-counter/display.html" target="_blank">
          http://localhost:3000/over%20bar/win-loss-counter/display.html
        </a>
      </div>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script>
    // عناصر الواجهة
    const winDisplay = document.getElementById('winDisplay');
    const lossDisplay = document.getElementById('lossDisplay');
    const winLabelDisplay = document.getElementById('winLabelDisplay');
    const lossLabelDisplay = document.getElementById('lossLabelDisplay');
    const winLabelInput = document.getElementById('winLabelInput');
    const lossLabelInput = document.getElementById('lossLabelInput');

    // الاتصال بـ WebSocket
    const socket = io('/overlay-bar');

    let isConnected = false;

    // متغيرات العدادات المحلية
    let currentCounters = {
      wins: 0,
      losses: 0,
      winLabel: 'المكاسب',
      lossLabel: 'الخسائر'
    };

    // حفظ البيانات عبر WebSocket
    function saveCounters(wins, losses, winLabel = null, lossLabel = null) {
      try {
        const data = {
          wins: Math.max(0, wins),
          losses: Math.max(0, losses),
          winLabel: winLabel || currentCounters.winLabel,
          lossLabel: lossLabel || currentCounters.lossLabel,
          lastUpdated: Date.now()
        };

        if (isConnected) {
          socket.emit('updateCounters', data);
          console.log('✅ تم إرسال البيانات عبر WebSocket:', data);
        } else {
          console.warn('❌ غير متصل بالخادم');
        }

        return true;
      } catch (error) {
        console.error('❌ خطأ في حفظ البيانات:', error);
        return false;
      }
    }
    
    // تحديث العرض
    function updateDisplay(data) {
      currentCounters = data;
      winDisplay.textContent = data.wins || 0;
      lossDisplay.textContent = data.losses || 0;

      // تحديث الأسماء
      if (data.winLabel) {
        winLabelDisplay.textContent = data.winLabel;
        winLabelInput.value = data.winLabel;
      }
      if (data.lossLabel) {
        lossLabelDisplay.textContent = data.lossLabel;
        lossLabelInput.value = data.lossLabel;
      }

      console.log('📊 تحديث العرض:', data);
    }

    // الأزرار السريعة
    function quickWin() {
      console.log('🏆 ضغط زر الفوز السريع');
      changeCounter('win', 1);
    }

    function quickLoss() {
      console.log('💔 ضغط زر الخسارة السريع');
      changeCounter('loss', 1);
    }

    // تغيير العداد
    function changeCounter(type, change) {
      console.log(`🔄 تغيير العداد: ${type} بـ ${change}`);

      let newWins = currentCounters.wins;
      let newLosses = currentCounters.losses;

      if (type === 'win') {
        newWins = Math.max(0, newWins + change);
      } else if (type === 'loss') {
        newLosses = Math.max(0, newLosses + change);
      }

      saveCounters(newWins, newLosses);
      console.log(`✅ ${type === 'win' ? '🏆' : '💔'} ${type}: ${type === 'win' ? newWins : newLosses}`);
    }

    // إعادة تعيين العدادات
    function resetCounters() {
      if (confirm('هل أنت متأكد من إعادة تعيين جميع العدادات؟')) {
        console.log('🔄 إعادة تعيين العدادات');
        saveCounters(0, 0);
        console.log('✅ تم إعادة تعيين جميع العدادات');
      }
    }

    // تحديث الأسماء
    function updateLabels() {
      const newWinLabel = winLabelInput.value.trim() || 'المكاسب';
      const newLossLabel = lossLabelInput.value.trim() || 'الخسائر';

      console.log('🏷️ تحديث الأسماء:', { winLabel: newWinLabel, lossLabel: newLossLabel });

      saveCounters(
        currentCounters.wins,
        currentCounters.losses,
        newWinLabel,
        newLossLabel
      );

      console.log('✅ تم تحديث الأسماء');
    }

    // أحداث WebSocket
    socket.on('connect', () => {
      console.log('🔗 متصل بخادم Overlay Bar');
      isConnected = true;
      socket.emit('getCounters');
    });

    socket.on('disconnect', () => {
      console.log('❌ انقطع الاتصال بخادم Overlay Bar');
      isConnected = false;
    });

    socket.on('counters', (data) => {
      console.log('📊 استقبال تحديث العدادات:', data);
      updateDisplay(data);
    });

    socket.on('connect_error', (error) => {
      console.error('خطأ في الاتصال:', error);
    });

    // نظام الاختصارات
    document.addEventListener('keydown', function(event) {
      // التحقق من Ctrl + Alt + Arrow Keys
      if (event.ctrlKey && event.altKey) {
        if (event.key === 'ArrowUp') {
          event.preventDefault();
          console.log('⌨️ اختصار: Ctrl+Alt+↑ - زيادة المكاسب');
          quickWin();
        } else if (event.key === 'ArrowDown') {
          event.preventDefault();
          console.log('⌨️ اختصار: Ctrl+Alt+↓ - زيادة الخسائر');
          quickLoss();
        }
      }
    });

    // تحديث العرض عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🎮 Win/Loss Counter Control Panel loaded');
      console.log('⌨️ الاختصارات متاحة: Ctrl+Alt+↑ للمكاسب، Ctrl+Alt+↓ للخسائر');
      if (isConnected) {
        socket.emit('getCounters');
      }
    });
  </script>
</body>
</html>
