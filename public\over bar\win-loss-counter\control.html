<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Win/Loss Counter - Control Panel</title>
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
      color: #333;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 10px;
    }

    .header p {
      font-size: 1.1rem;
      opacity: 0.9;
    }

    .content {
      padding: 40px;
    }

    .section {
      margin-bottom: 40px;
      padding: 30px;
      background: #f8f9fa;
      border-radius: 15px;
      border: 1px solid #e9ecef;
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 20px;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .counter-display {
      display: flex;
      justify-content: center;
      gap: 60px;
      margin-bottom: 30px;
    }

    .counter-item {
      text-align: center;
      padding: 20px;
      background: white;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      min-width: 150px;
    }

    .counter-icon {
      font-size: 3rem;
      margin-bottom: 10px;
    }

    .counter-label {
      font-size: 1.2rem;
      font-weight: 500;
      margin-bottom: 10px;
    }

    .counter-value {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 15px;
    }

    .win-counter {
      color: #27ae60;
    }

    .loss-counter {
      color: #e74c3c;
    }

    .controls {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 20px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      font-family: 'Tajawal', Arial, sans-serif;
    }

    .btn-success {
      background: #27ae60;
      color: white;
    }

    .btn-success:hover {
      background: #219a52;
      transform: translateY(-2px);
    }

    .btn-danger {
      background: #e74c3c;
      color: white;
    }

    .btn-danger:hover {
      background: #c0392b;
      transform: translateY(-2px);
    }

    .btn-warning {
      background: #f39c12;
      color: white;
    }

    .btn-warning:hover {
      background: #d68910;
      transform: translateY(-2px);
    }

    .btn-secondary {
      background: #95a5a6;
      color: white;
    }

    .btn-secondary:hover {
      background: #7f8c8d;
      transform: translateY(-2px);
    }

    .hotkeys-info {
      background: #e8f4fd;
      border: 1px solid #bee5eb;
      border-radius: 10px;
      padding: 20px;
      margin-top: 20px;
    }

    .hotkey-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #dee2e6;
    }

    .hotkey-item:last-child {
      border-bottom: none;
    }

    .hotkey-key {
      background: #343a40;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 0.9rem;
    }

    .status {
      text-align: center;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      font-weight: 500;
    }

    .status.connected {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status.disconnected {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .overlay-link {
      background: #17a2b8;
      color: white;
      padding: 15px;
      border-radius: 10px;
      text-align: center;
      margin-top: 20px;
    }

    .overlay-link a {
      color: white;
      text-decoration: none;
      font-weight: 500;
    }

    .overlay-link a:hover {
      text-decoration: underline;
    }

    @media (max-width: 768px) {
      .counter-display {
        flex-direction: column;
        gap: 20px;
      }
      
      .content {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🏆💔 Win/Loss Counter</h1>
      <p>لوحة التحكم في عدادات المكاسب والخسائر</p>
    </div>

    <div class="content">
      <!-- حالة الاتصال -->
      <div id="connectionStatus" class="status disconnected">
        ❌ غير متصل بالخادم
      </div>

      <!-- عرض العدادات الحالية -->
      <div class="section">
        <div class="section-title">
          📊 العدادات الحالية
        </div>
        
        <div class="counter-display">
          <div class="counter-item">
            <div class="counter-icon">🏆</div>
            <div class="counter-label">المكاسب</div>
            <div class="counter-value win-counter" id="winDisplay">0</div>
            <div class="controls">
              <button class="btn btn-success" onclick="changeCounter('win', 1)">+</button>
              <button class="btn btn-danger" onclick="changeCounter('win', -1)">-</button>
            </div>
          </div>

          <div class="counter-item">
            <div class="counter-icon">💔</div>
            <div class="counter-label">الخسائر</div>
            <div class="counter-value loss-counter" id="lossDisplay">0</div>
            <div class="controls">
              <button class="btn btn-success" onclick="changeCounter('loss', 1)">+</button>
              <button class="btn btn-danger" onclick="changeCounter('loss', -1)">-</button>
            </div>
          </div>
        </div>

        <div style="text-align: center;">
          <button class="btn btn-warning" onclick="resetCounters()">🔄 إعادة تعيين الكل</button>
        </div>
      </div>

      <!-- معلومات المفاتيح السريعة -->
      <div class="section">
        <div class="section-title">
          ⌨️ المفاتيح السريعة (تعمل من أي مكان)
        </div>
        
        <div class="hotkeys-info">
          <div class="hotkey-item">
            <span>زيادة المكاسب</span>
            <span class="hotkey-key">F9</span>
          </div>
          <div class="hotkey-item">
            <span>تقليل المكاسب</span>
            <span class="hotkey-key">F10</span>
          </div>
          <div class="hotkey-item">
            <span>زيادة الخسائر</span>
            <span class="hotkey-key">F11</span>
          </div>
          <div class="hotkey-item">
            <span>تقليل الخسائر</span>
            <span class="hotkey-key">F12</span>
          </div>
          <div class="hotkey-item">
            <span>إعادة تعيين الكل</span>
            <span class="hotkey-key">Ctrl + F12</span>
          </div>
        </div>
      </div>

      <!-- رابط الـ Overlay -->
      <div class="overlay-link">
        <strong>رابط العرض للـ OBS:</strong><br>
        <a href="/over%20bar/win-loss-counter/display.html" target="_blank">
          http://localhost:3000/over%20bar/win-loss-counter/display.html
        </a>
      </div>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script>
    // الاتصال بـ Socket.IO
    const socket = io('/overlay-bar');
    
    // عناصر الواجهة
    const connectionStatus = document.getElementById('connectionStatus');
    const winDisplay = document.getElementById('winDisplay');
    const lossDisplay = document.getElementById('lossDisplay');
    
    // تحديث حالة الاتصال
    function updateConnectionStatus(connected) {
      if (connected) {
        connectionStatus.className = 'status connected';
        connectionStatus.innerHTML = '✅ متصل بالخادم';
      } else {
        connectionStatus.className = 'status disconnected';
        connectionStatus.innerHTML = '❌ غير متصل بالخادم';
      }
    }
    
    // تغيير العداد
    function changeCounter(type, change) {
      socket.emit('changeCounter', { type, change });
    }
    
    // إعادة تعيين العدادات
    function resetCounters() {
      if (confirm('هل أنت متأكد من إعادة تعيين جميع العدادات؟')) {
        socket.emit('resetCounters');
      }
    }
    
    // أحداث Socket.IO
    socket.on('connect', () => {
      console.log('🔗 متصل بخادم Win/Loss Counter');
      updateConnectionStatus(true);
      socket.emit('getCounters');
    });
    
    socket.on('disconnect', () => {
      console.log('❌ انقطع الاتصال بخادم Win/Loss Counter');
      updateConnectionStatus(false);
    });
    
    socket.on('currentCounters', (data) => {
      winDisplay.textContent = data.wins || 0;
      lossDisplay.textContent = data.losses || 0;
    });
    
    socket.on('counterUpdate', (data) => {
      if (data.type === 'win') {
        winDisplay.textContent = data.value;
      } else if (data.type === 'loss') {
        lossDisplay.textContent = data.value;
      } else if (data.type === 'both') {
        winDisplay.textContent = data.wins;
        lossDisplay.textContent = data.losses;
      }
    });
  </script>
</body>
</html>
