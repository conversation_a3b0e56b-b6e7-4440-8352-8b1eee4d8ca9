/**
 * Overlay Data Updater
 * سكريبت بسيط لتحديث ملف البيانات
 */

// هذا الملف يعمل في المتصفح لتحديث ملف JSON
// يستخدم localStorage كنسخة احتياطية

class OverlayDataManager {
  constructor() {
    this.dataFile = '/over%20bar/data.json';
    this.storageKey = 'overlayBar_winLossCounter';
  }

  // قراءة البيانات
  async loadData() {
    try {
      const response = await fetch(this.dataFile + '?t=' + Date.now());
      if (response.ok) {
        const data = await response.json();
        return data.winLossCounter || { wins: 0, losses: 0 };
      }
    } catch (error) {
      console.warn('فشل في قراءة الملف، استخدام localStorage:', error);
    }
    
    // استخدام localStorage كبديل
    try {
      const saved = localStorage.getItem(this.storageKey);
      if (saved) {
        const data = JSON.parse(saved);
        return { wins: data.wins || 0, losses: data.losses || 0 };
      }
    } catch (error) {
      console.error('خطأ في localStorage:', error);
    }
    
    return { wins: 0, losses: 0 };
  }

  // حفظ البيانات
  async saveData(wins, losses) {
    const data = {
      wins: Math.max(0, wins),
      losses: Math.max(0, losses),
      lastUpdated: Date.now()
    };

    // حفظ في localStorage
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.error('خطأ في حفظ localStorage:', error);
    }

    // محاولة تحديث الملف (يحتاج خادم)
    try {
      const fileData = {
        winLossCounter: data
      };
      
      // هذا لن يعمل في المتصفح العادي - فقط للتوضيح
      await this.updateJSONFile(fileData);
    } catch (error) {
      console.warn('لا يمكن كتابة الملف مباشرة:', error);
    }

    return true;
  }

  // تحديث ملف JSON (يحتاج خادم)
  async updateJSONFile(data) {
    // هذا مجرد محاولة - لن يعمل في المتصفح العادي
    // يحتاج endpoint في الخادم
    const response = await fetch('/update-overlay-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      throw new Error('فشل في تحديث الملف');
    }
    
    return response.json();
  }

  // تزامن البيانات بين localStorage والملف
  async syncData() {
    try {
      const fileData = await this.loadData();
      const localData = JSON.parse(localStorage.getItem(this.storageKey) || '{}');
      
      // إذا كان localStorage أحدث، استخدمه
      if (localData.lastUpdated > (fileData.lastUpdated || 0)) {
        return { wins: localData.wins || 0, losses: localData.losses || 0 };
      }
      
      // وإلا استخدم بيانات الملف
      return fileData;
    } catch (error) {
      console.error('خطأ في التزامن:', error);
      return { wins: 0, losses: 0 };
    }
  }
}

// إنشاء instance عام
window.overlayDataManager = new OverlayDataManager();

// تصدير للاستخدام في Node.js إذا لزم الأمر
if (typeof module !== 'undefined' && module.exports) {
  module.exports = OverlayDataManager;
}
