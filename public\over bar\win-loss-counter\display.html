<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Win/Loss Counter - Display</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: transparent;
      font-family: 'Arial', sans-serif;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .win-loss-counter {
      background: rgba(0, 0, 0, 0.8);
      border-radius: 15px;
      padding: 15px 30px;
      display: flex;
      align-items: center;
      gap: 40px;
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      min-width: 400px;
      transition: all 0.3s ease;
    }

    .counter-section {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 24px;
      font-weight: bold;
      color: white;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .win-section {
      color: #00ff88;
    }

    .loss-section {
      color: #ff4757;
    }

    .counter-icon {
      font-size: 28px;
      filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
    }

    .counter-number {
      font-size: 32px;
      font-weight: 900;
      min-width: 60px;
      text-align: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 5px 10px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
    }

    .separator {
      width: 2px;
      height: 40px;
      background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.3), transparent);
    }

    /* تأثيرات التحديث */
    .counter-number.updated {
      animation: pulse 0.4s ease;
    }

    @keyframes pulse {
      0% { 
        transform: scale(1); 
      }
      50% { 
        transform: scale(1.15); 
        background: rgba(255, 255, 255, 0.3);
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
      }
      100% { 
        transform: scale(1); 
      }
    }

    /* تأثير الزيادة */
    .counter-number.increment {
      animation: incrementEffect 0.5s ease;
    }

    @keyframes incrementEffect {
      0% { 
        transform: scale(1) translateY(0); 
      }
      30% { 
        transform: scale(1.2) translateY(-5px); 
        color: #00ff88;
        text-shadow: 0 0 15px #00ff88;
      }
      100% { 
        transform: scale(1) translateY(0); 
      }
    }

    /* تأثير النقصان */
    .counter-number.decrement {
      animation: decrementEffect 0.5s ease;
    }

    @keyframes decrementEffect {
      0% { 
        transform: scale(1) translateY(0); 
      }
      30% { 
        transform: scale(0.8) translateY(5px); 
        color: #ff4757;
        text-shadow: 0 0 15px #ff4757;
      }
      100% { 
        transform: scale(1) translateY(0); 
      }
    }

    /* تأثيرات الهوفر للمعاينة */
    .win-loss-counter:hover {
      transform: scale(1.02);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    }
  </style>
</head>
<body>
  <div class="win-loss-counter">
    <div class="counter-section win-section">
      <span class="counter-icon">🏆</span>
      <span id="winLabel">المكاسب:</span>
      <span class="counter-number" id="winCounter">0</span>
    </div>

    <div class="separator"></div>

    <div class="counter-section loss-section">
      <span class="counter-icon">💔</span>
      <span id="lossLabel">الخسائر:</span>
      <span class="counter-number" id="lossCounter">0</span>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script>
    // عناصر العداد
    const winCounter = document.getElementById('winCounter');
    const lossCounter = document.getElementById('lossCounter');
    const winLabel = document.getElementById('winLabel');
    const lossLabel = document.getElementById('lossLabel');

    // الاتصال بـ WebSocket
    const socket = io('/overlay-bar');

    let isConnected = false;

    // تحديث العرض من بيانات WebSocket
    function updateDisplay(data) {
      updateCounter(winCounter, data.wins || 0, 'updated');
      updateCounter(lossCounter, data.losses || 0, 'updated');

      // تحديث الأسماء
      if (data.winLabel) {
        winLabel.textContent = data.winLabel + ':';
      }
      if (data.lossLabel) {
        lossLabel.textContent = data.lossLabel + ':';
      }
    }

    // تحديث العدادات مع تأثيرات
    function updateCounter(element, value, effect = 'updated') {
      element.textContent = value;
      element.classList.add(effect);
      setTimeout(() => {
        element.classList.remove(effect);
      }, effect === 'updated' ? 400 : 500);
    }

    // أحداث WebSocket
    socket.on('connect', () => {
      console.log('🔗 متصل بخادم Overlay Bar');
      isConnected = true;
      socket.emit('getCounters');
    });

    socket.on('disconnect', () => {
      console.log('❌ انقطع الاتصال بخادم Overlay Bar');
      isConnected = false;
    });

    socket.on('counters', (data) => {
      console.log('📊 تحديث العدادات:', data);
      updateDisplay(data);
    });

    socket.on('connect_error', (error) => {
      console.error('خطأ في الاتصال:', error);
    });

    // نظام الاختصارات العام
    document.addEventListener('keydown', function(event) {
      // التحقق من Ctrl + Alt + Arrow Keys
      if (event.ctrlKey && event.altKey) {
        if (event.key === 'ArrowUp') {
          event.preventDefault();
          console.log('⌨️ اختصار: Ctrl+Alt+↑ - زيادة المكاسب');
          if (isConnected) {
            socket.emit('updateCounters', {
              wins: (winCounter.textContent ? parseInt(winCounter.textContent) : 0) + 1,
              losses: lossCounter.textContent ? parseInt(lossCounter.textContent) : 0,
              winLabel: winLabel.textContent.replace(':', ''),
              lossLabel: lossLabel.textContent.replace(':', ''),
              lastUpdated: Date.now()
            });
          }
        } else if (event.key === 'ArrowDown') {
          event.preventDefault();
          console.log('⌨️ اختصار: Ctrl+Alt+↓ - زيادة الخسائر');
          if (isConnected) {
            socket.emit('updateCounters', {
              wins: winCounter.textContent ? parseInt(winCounter.textContent) : 0,
              losses: (lossCounter.textContent ? parseInt(lossCounter.textContent) : 0) + 1,
              winLabel: winLabel.textContent.replace(':', ''),
              lossLabel: lossLabel.textContent.replace(':', ''),
              lastUpdated: Date.now()
            });
          }
        }
      }
    });

    // تحميل القيم عند بدء التشغيل
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🎮 Win/Loss Counter Display loaded');
      console.log('⌨️ الاختصارات متاحة: Ctrl+Alt+↑ للمكاسب، Ctrl+Alt+↓ للخسائر');
      if (isConnected) {
        socket.emit('getCounters');
      }
    });
  </script>
</body>
</html>
