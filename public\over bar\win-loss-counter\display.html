<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Win/Loss Counter - Display</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: transparent;
      font-family: 'Arial', sans-serif;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .win-loss-counter {
      background: rgba(0, 0, 0, 0.8);
      border-radius: 15px;
      padding: 15px 30px;
      display: flex;
      align-items: center;
      gap: 40px;
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      min-width: 400px;
      transition: all 0.3s ease;
    }

    .counter-section {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 24px;
      font-weight: bold;
      color: white;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .win-section {
      color: #00ff88;
    }

    .loss-section {
      color: #ff4757;
    }

    .counter-icon {
      font-size: 28px;
      filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
    }

    .counter-number {
      font-size: 32px;
      font-weight: 900;
      min-width: 60px;
      text-align: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 5px 10px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
    }

    .separator {
      width: 2px;
      height: 40px;
      background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.3), transparent);
    }

    /* تأثيرات التحديث */
    .counter-number.updated {
      animation: pulse 0.4s ease;
    }

    @keyframes pulse {
      0% { 
        transform: scale(1); 
      }
      50% { 
        transform: scale(1.15); 
        background: rgba(255, 255, 255, 0.3);
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
      }
      100% { 
        transform: scale(1); 
      }
    }

    /* تأثير الزيادة */
    .counter-number.increment {
      animation: incrementEffect 0.5s ease;
    }

    @keyframes incrementEffect {
      0% { 
        transform: scale(1) translateY(0); 
      }
      30% { 
        transform: scale(1.2) translateY(-5px); 
        color: #00ff88;
        text-shadow: 0 0 15px #00ff88;
      }
      100% { 
        transform: scale(1) translateY(0); 
      }
    }

    /* تأثير النقصان */
    .counter-number.decrement {
      animation: decrementEffect 0.5s ease;
    }

    @keyframes decrementEffect {
      0% { 
        transform: scale(1) translateY(0); 
      }
      30% { 
        transform: scale(0.8) translateY(5px); 
        color: #ff4757;
        text-shadow: 0 0 15px #ff4757;
      }
      100% { 
        transform: scale(1) translateY(0); 
      }
    }

    /* تأثيرات الهوفر للمعاينة */
    .win-loss-counter:hover {
      transform: scale(1.02);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    }
  </style>
</head>
<body>
  <div class="win-loss-counter">
    <div class="counter-section win-section">
      <span class="counter-icon">🏆</span>
      <span>المكاسب:</span>
      <span class="counter-number" id="winCounter">0</span>
    </div>
    
    <div class="separator"></div>
    
    <div class="counter-section loss-section">
      <span class="counter-icon">💔</span>
      <span>الخسائر:</span>
      <span class="counter-number" id="lossCounter">0</span>
    </div>
  </div>

  <script src="/over%20bar/file-sync.js"></script>
  <script>
    // عناصر العداد
    const winCounter = document.getElementById('winCounter');
    const lossCounter = document.getElementById('lossCounter');

    // تحميل البيانات باستخدام نظام المزامنة
    async function loadCounters() {
      try {
        if (window.OverlayFileSync) {
          const data = await window.OverlayFileSync.sync();
          return {
            wins: data.wins || 0,
            losses: data.losses || 0
          };
        }

        // fallback إلى قراءة الملف مباشرة
        const response = await fetch('/over%20bar/data.json?t=' + Date.now());
        if (response.ok) {
          const data = await response.json();
          return {
            wins: data.winLossCounter?.wins || 0,
            losses: data.winLossCounter?.losses || 0
          };
        }
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
      }
      return { wins: 0, losses: 0 };
    }

    // تحديث العدادات مع تأثيرات
    function updateCounter(element, value, effect = 'updated') {
      element.textContent = value;
      element.classList.add(effect);
      setTimeout(() => {
        element.classList.remove(effect);
      }, effect === 'updated' ? 400 : 500);
    }

    // تحديث العرض
    async function updateDisplay() {
      const counters = await loadCounters();
      updateCounter(winCounter, counters.wins, 'updated');
      updateCounter(lossCounter, counters.losses, 'updated');
    }

    // مراقبة تغييرات localStorage
    window.addEventListener('storage', function(e) {
      if (e.key === 'overlayBar_winLossCounter') {
        updateDisplay();
      }
    });

    // تحديث دوري للتأكد من التزامن
    setInterval(updateDisplay, 1000);

    // تحميل القيم عند بدء التشغيل
    document.addEventListener('DOMContentLoaded', function() {
      updateDisplay();
      console.log('🎮 Win/Loss Counter Display loaded');
    });
  </script>
</body>
</html>
