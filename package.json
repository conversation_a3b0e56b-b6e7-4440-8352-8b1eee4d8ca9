{"name": "streamtok", "version": "1.0.0", "description": "StreamTok - Advanced Live Streaming Overlay System", "main": "electron-main.js", "scripts": {"start": "node index.js", "electron": "electron .", "electron-dev": "electron . --dev", "build": "electron-builder --config.asar=false", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["tiktok", "live", "overlay", "obs", "streaming"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@jitsi/robotjs": "^0.6.16", "axios": "^1.9.0", "body-parser": "^2.2.0", "dotenv": "^16.4.5", "express": "^4.21.2", "firebase": "^11.8.1", "fs-extra": "^11.3.0", "libretranslate": "^1.0.1", "multer": "^2.0.0", "node-edge-tts": "^1.2.8", "node-global-key-listener": "^0.3.0", "p-queue": "^8.1.0", "play-sound": "^1.1.6", "robotjs": "^0.6.0", "say": "^0.16.0", "socket.io": "^4.8.1", "sound-play": "^1.1.0", "tiktok-live-connector": "^2.0.3", "uuid": "^9.0.1"}, "build": {"appId": "com.streamtok.streamtok", "productName": "StreamTok", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/@jitsi/robotjs/build", "!node_modules/robotjs/build"], "extraFiles": [{"from": "node_modules/electron/dist/node.exe", "to": "node.exe"}], "win": {"target": "nsis", "icon": "public/favicon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": false, "asar": false, "beforeBuild": "echo 'Skipping native rebuild'"}, "devDependencies": {"electron": "^28.3.3"}}