# 🎮 Overlay Bar - Global Hotkeys Setup

## 📋 متطلبات النظام

لتفعيل الاختصارات العالمية (Global Hotkeys) التي تعمل من أي مكان، حتى أثناء اللعب:

### 🔧 تثبيت المكتبة المطلوبة

```bash
npm install node-global-key-listener
```

## ⌨️ الاختصارات العالمية

بعد تثبيت المكتبة، ستعمل هذه الاختصارات من **أي مكان**:

- **Ctrl + Alt + ↑** ← زيادة المكاسب
- **Ctrl + Alt + ↓** ← زيادة الخسائر

### ✨ المميزات:

- ✅ **تعمل من أي لعبة** - لا تحتاج للخروج من اللعبة
- ✅ **تعمل من أي برنامج** - حتى لو كان البرنامج في وضع ملء الشاشة
- ✅ **تزامن فوري** - التحديثات تظهر فوراً في OBS
- ✅ **لا تتداخل مع الألعاب** - اختصارات آمنة

## 🚀 طريقة التشغيل

1. **تثبيت المكتبة:**
   ```bash
   npm install node-global-key-listener
   ```

2. **تشغيل الخادم:**
   ```bash
   node index.js
   ```

3. **ستظهر رسالة التأكيد:**
   ```
   ✅ Global Hotkeys مفعل: Ctrl+Alt+↑ للمكاسب، Ctrl+Alt+↓ للخسائر
   ⌨️ Global Hotkeys: Ctrl+Alt+↑ للمكاسب، Ctrl+Alt+↓ للخسائر
   ```

## 🎯 الاستخدام

### أثناء اللعب:
- اضغط **Ctrl + Alt + ↑** عند الفوز
- اضغط **Ctrl + Alt + ↓** عند الخسارة
- **لا تحتاج للخروج من اللعبة!**

### في OBS:
- أضف رابط العرض: `http://localhost:3000/over%20bar/win-loss-counter/display.html`
- ستظهر التحديثات فوراً

## ⚠️ ملاحظات مهمة

### إذا لم تعمل المكتبة:
- **Windows**: قد تحتاج تشغيل البرنامج كـ Administrator
- **macOS**: قد تحتاج إذن Accessibility في System Preferences
- **Linux**: قد تحتاج تثبيت X11 development libraries

### البدائل:
إذا لم تعمل Global Hotkeys، يمكنك استخدام:
- **الأزرار في لوحة التحكم** - `http://localhost:3000/over%20bar/win-loss-counter/control.html`
- **الاختصارات المحلية** - تعمل فقط عندما تكون في نافذة المتصفح

## 🔧 استكشاف الأخطاء

### إذا ظهرت رسالة "Global Hotkeys غير متاح":
```bash
# تأكد من تثبيت المكتبة
npm install node-global-key-listener

# أعد تشغيل الخادم
node index.js
```

### إذا لم تعمل الاختصارات:
1. تأكد من ظهور رسالة التأكيد عند تشغيل الخادم
2. جرب تشغيل البرنامج كـ Administrator (Windows)
3. تحقق من إعدادات الأمان في النظام

## 📞 الدعم

إذا واجهت مشاكل:
1. تأكد من تثبيت المكتبة بشكل صحيح
2. أعد تشغيل الخادم
3. تحقق من رسائل الخطأ في Console

---

**الآن يمكنك التحكم في العدادات من أي مكان! 🎮🚀**
