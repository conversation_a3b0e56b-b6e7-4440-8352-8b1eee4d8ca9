/**
 * Overlay Bar Simple Server
 * خادم بسيط لنظام Overlay Bar
 */

const express = require('express');
const fs = require('fs');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3001; // منفذ منفصل

// إعداد middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// مسار ملف البيانات
const DATA_FILE = path.join(__dirname, 'public', 'over bar', 'data.json');

// التأكد من وجود ملف البيانات
function ensureDataFile() {
  try {
    if (!fs.existsSync(DATA_FILE)) {
      const initialData = {
        winLossCounter: {
          wins: 0,
          losses: 0,
          lastUpdated: Date.now()
        }
      };
      fs.writeFileSync(DATA_FILE, JSON.stringify(initialData, null, 2));
      console.log('✅ تم إنشاء ملف البيانات');
    }
  } catch (error) {
    console.error('❌ خطأ في إنشاء ملف البيانات:', error);
  }
}

// قراءة البيانات
function readData() {
  try {
    const data = fs.readFileSync(DATA_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('❌ خطأ في قراءة البيانات:', error);
    return {
      winLossCounter: {
        wins: 0,
        losses: 0,
        lastUpdated: Date.now()
      }
    };
  }
}

// كتابة البيانات
function writeData(data) {
  try {
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error('❌ خطأ في كتابة البيانات:', error);
    return false;
  }
}

// API Routes

// GET - قراءة العدادات
app.get('/api/counters', (req, res) => {
  const data = readData();
  res.json({
    success: true,
    data: data.winLossCounter
  });
});

// POST - تحديث العدادات
app.post('/api/counters', (req, res) => {
  try {
    const { wins, losses } = req.body;
    
    if (typeof wins !== 'number' || typeof losses !== 'number') {
      return res.status(400).json({
        success: false,
        error: 'القيم يجب أن تكون أرقام'
      });
    }
    
    const data = readData();
    data.winLossCounter = {
      wins: Math.max(0, wins),
      losses: Math.max(0, losses),
      lastUpdated: Date.now()
    };
    
    const saved = writeData(data);
    
    if (saved) {
      console.log(`📊 تحديث العدادات: المكاسب=${wins}, الخسائر=${losses}`);
      res.json({
        success: true,
        data: data.winLossCounter
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'فشل في حفظ البيانات'
      });
    }
  } catch (error) {
    console.error('❌ خطأ في تحديث العدادات:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم'
    });
  }
});

// PUT - تغيير عداد محدد
app.put('/api/counters/:type', (req, res) => {
  try {
    const { type } = req.params;
    const { change } = req.body;
    
    if (!['win', 'loss'].includes(type)) {
      return res.status(400).json({
        success: false,
        error: 'نوع العداد غير صحيح'
      });
    }
    
    if (typeof change !== 'number') {
      return res.status(400).json({
        success: false,
        error: 'التغيير يجب أن يكون رقم'
      });
    }
    
    const data = readData();
    
    if (type === 'win') {
      data.winLossCounter.wins = Math.max(0, data.winLossCounter.wins + change);
    } else {
      data.winLossCounter.losses = Math.max(0, data.winLossCounter.losses + change);
    }
    
    data.winLossCounter.lastUpdated = Date.now();
    
    const saved = writeData(data);
    
    if (saved) {
      const newValue = type === 'win' ? data.winLossCounter.wins : data.winLossCounter.losses;
      console.log(`${type === 'win' ? '🏆' : '💔'} ${type}: ${newValue}`);
      
      res.json({
        success: true,
        data: data.winLossCounter
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'فشل في حفظ البيانات'
      });
    }
  } catch (error) {
    console.error('❌ خطأ في تغيير العداد:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم'
    });
  }
});

// DELETE - إعادة تعيين العدادات
app.delete('/api/counters', (req, res) => {
  try {
    const data = readData();
    data.winLossCounter = {
      wins: 0,
      losses: 0,
      lastUpdated: Date.now()
    };
    
    const saved = writeData(data);
    
    if (saved) {
      console.log('🔄 تم إعادة تعيين جميع العدادات');
      res.json({
        success: true,
        data: data.winLossCounter
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'فشل في حفظ البيانات'
      });
    }
  } catch (error) {
    console.error('❌ خطأ في إعادة التعيين:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم'
    });
  }
});

// تهيئة الخادم
ensureDataFile();

app.listen(PORT, () => {
  console.log(`🎮 Overlay Bar Server running on http://localhost:${PORT}`);
  console.log(`📊 API متاح على http://localhost:${PORT}/api/counters`);
});

module.exports = app;
