/**
 * Overlay Bar - Utility Functions
 * دوال مساعدة لنظام Overlay Bar
 */

// ===== SOCKET.IO UTILITIES =====

/**
 * إنشاء اتصال Socket.IO للـ Overlay Bar
 * @param {string} namespace - اسم الـ namespace (افتراضي: '/overlay-bar')
 * @returns {Socket} كائن Socket.IO
 */
function createOverlayBarSocket(namespace = '/overlay-bar') {
  if (typeof io === 'undefined') {
    console.error('Socket.IO غير متوفر. تأكد من تحميل المكتبة.');
    return null;
  }
  
  const socket = io(namespace);
  
  // إعداد معالجات الأحداث الأساسية
  socket.on('connect', () => {
    console.log(`🔗 متصل بـ Overlay Bar (${namespace})`);
  });
  
  socket.on('disconnect', () => {
    console.log(`❌ انقطع الاتصال بـ Overlay Bar (${namespace})`);
  });
  
  socket.on('connect_error', (error) => {
    console.error('خطأ في الاتصال بـ Overlay Bar:', error);
  });
  
  return socket;
}

// ===== DOM UTILITIES =====

/**
 * إضافة كلاس CSS مع إزالة تلقائية بعد مدة
 * @param {HTMLElement} element - العنصر
 * @param {string} className - اسم الكلاس
 * @param {number} duration - المدة بالميلي ثانية (افتراضي: 300)
 */
function addTemporaryClass(element, className, duration = 300) {
  if (!element || !className) return;
  
  element.classList.add(className);
  setTimeout(() => {
    element.classList.remove(className);
  }, duration);
}

/**
 * تحديث نص عنصر مع تأثير بصري
 * @param {HTMLElement} element - العنصر
 * @param {string|number} newText - النص الجديد
 * @param {string} effectClass - كلاس التأثير (افتراضي: 'updated')
 */
function updateElementWithEffect(element, newText, effectClass = 'updated') {
  if (!element) return;
  
  element.textContent = newText;
  addTemporaryClass(element, effectClass);
}

/**
 * إنشاء عنصر HTML مع خصائص
 * @param {string} tag - نوع العنصر
 * @param {Object} attributes - الخصائص
 * @param {string} content - المحتوى النصي
 * @returns {HTMLElement} العنصر المنشأ
 */
function createElement(tag, attributes = {}, content = '') {
  const element = document.createElement(tag);
  
  // إضافة الخصائص
  Object.keys(attributes).forEach(key => {
    if (key === 'className') {
      element.className = attributes[key];
    } else if (key === 'style' && typeof attributes[key] === 'object') {
      Object.assign(element.style, attributes[key]);
    } else {
      element.setAttribute(key, attributes[key]);
    }
  });
  
  // إضافة المحتوى
  if (content) {
    element.textContent = content;
  }
  
  return element;
}

// ===== ANIMATION UTILITIES =====

/**
 * تشغيل تأثير النبض على عنصر
 * @param {HTMLElement} element - العنصر
 * @param {number} duration - مدة التأثير (افتراضي: 400)
 */
function pulseElement(element, duration = 400) {
  if (!element) return;
  
  const keyframes = [
    { transform: 'scale(1)', opacity: '1' },
    { transform: 'scale(1.1)', opacity: '0.8' },
    { transform: 'scale(1)', opacity: '1' }
  ];
  
  const options = {
    duration: duration,
    easing: 'ease-in-out'
  };
  
  element.animate(keyframes, options);
}

/**
 * تشغيل تأثير الاهتزاز على عنصر
 * @param {HTMLElement} element - العنصر
 * @param {number} intensity - شدة الاهتزاز (افتراضي: 5)
 */
function shakeElement(element, intensity = 5) {
  if (!element) return;
  
  const keyframes = [
    { transform: 'translateX(0)' },
    { transform: `translateX(-${intensity}px)` },
    { transform: `translateX(${intensity}px)` },
    { transform: `translateX(-${intensity}px)` },
    { transform: 'translateX(0)' }
  ];
  
  const options = {
    duration: 300,
    easing: 'ease-in-out'
  };
  
  element.animate(keyframes, options);
}

// ===== STORAGE UTILITIES =====

/**
 * حفظ بيانات في التخزين المحلي
 * @param {string} key - المفتاح
 * @param {any} data - البيانات
 * @returns {boolean} نجح الحفظ أم لا
 */
function saveToStorage(key, data) {
  try {
    const jsonData = JSON.stringify(data);
    localStorage.setItem(key, jsonData);
    return true;
  } catch (error) {
    console.error('خطأ في حفظ البيانات:', error);
    return false;
  }
}

/**
 * تحميل بيانات من التخزين المحلي
 * @param {string} key - المفتاح
 * @param {any} defaultValue - القيمة الافتراضية
 * @returns {any} البيانات المحملة
 */
function loadFromStorage(key, defaultValue = null) {
  try {
    const jsonData = localStorage.getItem(key);
    return jsonData ? JSON.parse(jsonData) : defaultValue;
  } catch (error) {
    console.error('خطأ في تحميل البيانات:', error);
    return defaultValue;
  }
}

/**
 * حذف بيانات من التخزين المحلي
 * @param {string} key - المفتاح
 * @returns {boolean} نجح الحذف أم لا
 */
function removeFromStorage(key) {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('خطأ في حذف البيانات:', error);
    return false;
  }
}

// ===== NUMBER UTILITIES =====

/**
 * تنسيق رقم للعرض
 * @param {number} num - الرقم
 * @param {Object} options - خيارات التنسيق
 * @returns {string} الرقم المنسق
 */
function formatNumber(num, options = {}) {
  const {
    decimals = 0,
    separator = ',',
    prefix = '',
    suffix = ''
  } = options;
  
  if (typeof num !== 'number' || isNaN(num)) {
    return '0';
  }
  
  let formatted = num.toFixed(decimals);
  
  // إضافة فاصل الآلاف
  if (separator) {
    formatted = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, separator);
  }
  
  return prefix + formatted + suffix;
}

/**
 * حساب النسبة المئوية
 * @param {number} value - القيمة
 * @param {number} total - المجموع
 * @param {number} decimals - عدد الخانات العشرية
 * @returns {number} النسبة المئوية
 */
function calculatePercentage(value, total, decimals = 1) {
  if (total === 0) return 0;
  return Number(((value / total) * 100).toFixed(decimals));
}

// ===== TIME UTILITIES =====

/**
 * تنسيق الوقت للعرض
 * @param {Date|number} time - الوقت
 * @param {Object} options - خيارات التنسيق
 * @returns {string} الوقت المنسق
 */
function formatTime(time, options = {}) {
  const {
    format = 'HH:MM:SS',
    locale = 'ar-SA'
  } = options;
  
  const date = time instanceof Date ? time : new Date(time);
  
  if (format === 'HH:MM:SS') {
    return date.toLocaleTimeString(locale);
  } else if (format === 'HH:MM') {
    return date.toLocaleTimeString(locale, { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  } else {
    return date.toLocaleString(locale);
  }
}

/**
 * حساب الفرق الزمني
 * @param {Date|number} start - وقت البداية
 * @param {Date|number} end - وقت النهاية (افتراضي: الآن)
 * @returns {Object} الفرق الزمني
 */
function timeDifference(start, end = new Date()) {
  const startDate = start instanceof Date ? start : new Date(start);
  const endDate = end instanceof Date ? end : new Date(end);
  
  const diff = Math.abs(endDate - startDate);
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  return {
    milliseconds: diff,
    seconds: seconds % 60,
    minutes: minutes % 60,
    hours: hours % 24,
    days: days,
    totalSeconds: seconds,
    totalMinutes: minutes,
    totalHours: hours
  };
}

// ===== VALIDATION UTILITIES =====

/**
 * التحقق من صحة رقم
 * @param {any} value - القيمة
 * @param {Object} options - خيارات التحقق
 * @returns {boolean} صحيح أم لا
 */
function isValidNumber(value, options = {}) {
  const {
    min = -Infinity,
    max = Infinity,
    integer = false
  } = options;
  
  const num = Number(value);
  
  if (isNaN(num)) return false;
  if (num < min || num > max) return false;
  if (integer && !Number.isInteger(num)) return false;
  
  return true;
}

// ===== EXPORT FOR NODE.JS =====
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    createOverlayBarSocket,
    addTemporaryClass,
    updateElementWithEffect,
    createElement,
    pulseElement,
    shakeElement,
    saveToStorage,
    loadFromStorage,
    removeFromStorage,
    formatNumber,
    calculatePercentage,
    formatTime,
    timeDifference,
    isValidNumber
  };
}
